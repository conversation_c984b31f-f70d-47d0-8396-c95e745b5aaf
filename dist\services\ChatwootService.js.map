{"version": 3, "file": "ChatwootService.js", "sourceRoot": "", "sources": ["../../src/services/ChatwootService.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA6C;AAC7C,4CAAyC;AAyDzC,MAAa,eAAe;IAClB,MAAM,CAAiB;IACvB,SAAS,CAAgB;IAEjC,YAAY,MAAsB;QAChC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,eAAK,CAAC,MAAM,CAAC;YAC5B,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;YAC5B,OAAO,EAAE;gBACP,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;gBAC9C,cAAc,EAAE,kBAAkB;aACnC;YACD,OAAO,EAAE,KAAK;SACf,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,mBAAmB,CAAC,WAKzB;QACC,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,sCAAsC,EAAE;gBAClD,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,UAAU,EAAE,WAAW,CAAC,UAAU;aACnC,CAAC,CAAC;YAGH,MAAM,OAAO,GAAG;gBACd,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,YAAY,EAAE,WAAW,CAAC,YAAY;gBACtC,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;gBAC7B,qBAAqB,EAAE;oBACrB,QAAQ,EAAE,WAAW,CAAC,UAAU;oBAChC,MAAM,EAAE,cAAc;iBACvB;aACF,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;YACxE,MAAM,OAAO,GAAoB,QAAQ,CAAC,IAAI,CAAC;YAE/C,eAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE;gBAChD,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,cAAc,EAAE,OAAO,CAAC,eAAe,EAAE,MAAM;aAChD,CAAC,CAAC;YAGH,MAAM,YAAY,GAAG,OAAO,CAAC,eAAe,EAAE,IAAI,CAChD,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,IAAI,CAAC,MAAM,CAAC,OAAO,CAC1C,CAAC;YAEF,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;YACxE,CAAC;YAED,OAAO;gBACL,OAAO;gBACP,QAAQ,EAAE,YAAY,CAAC,SAAS;aACjC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBAC5C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI;aAC/B,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,QAAgB;QACvC,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAE/D,MAAM,OAAO,GAAG;gBACd,SAAS,EAAE,QAAQ;gBACnB,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;aAC9B,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAC;YAC7E,MAAM,YAAY,GAAyB,QAAQ,CAAC,IAAI,CAAC;YAEzD,eAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE;gBAC/C,cAAc,EAAE,YAAY,CAAC,EAAE;aAChC,CAAC,CAAC;YAEH,OAAO,YAAY,CAAC;QAEtB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBAC5C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI;gBAC9B,QAAQ;aACT,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,aAAa,CACjB,cAAsB,EACtB,OAAe,EACf,cAAuC,UAAU;QAEjD,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBAC1C,cAAc;gBACd,WAAW;gBACX,aAAa,EAAE,OAAO,CAAC,MAAM;aAC9B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG;gBACd,OAAO,EAAE,OAAO;gBAChB,YAAY,EAAE,WAAW;gBACzB,OAAO,EAAE,KAAK;aACf,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CACxC,yBAAyB,cAAc,WAAW,EAClD,OAAO,CACR,CAAC;YACF,MAAM,OAAO,GAAoB,QAAQ,CAAC,IAAI,CAAC;YAE/C,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBAC1C,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,cAAc,EAAE,OAAO,CAAC,eAAe;aACxC,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC;QAEjB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBACvC,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI;gBAC9B,cAAc;aACf,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAAC,SAAiB;QAC9C,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;YAE7D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,uBAAuB,EAAE;gBACjE,MAAM,EAAE;oBACN,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;oBAC7B,MAAM,EAAE,MAAM;iBACf;aACF,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;YAG/C,MAAM,oBAAoB,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,IAAS,EAAE,EAAE,CAC5D,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,KAAK,SAAS,CACpC,CAAC;YAEF,IAAI,oBAAoB,EAAE,CAAC;gBACzB,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;oBACzC,cAAc,EAAE,oBAAoB,CAAC,EAAE;iBACxC,CAAC,CAAC;gBACH,OAAO,oBAAoB,CAAC;YAC9B,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAC9C,OAAO,IAAI,CAAC;QAEd,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE;gBACnD,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS;aACV,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,WAMxB;QAKC,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,sCAAsC,EAAE;gBAClD,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,SAAS,EAAE,WAAW,CAAC,SAAS;aACjC,CAAC,CAAC;YAGH,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC;gBAC3D,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,UAAU,EAAE,WAAW,CAAC,MAAM;gBAC9B,YAAY,EAAE,SAAS;gBACvB,KAAK,EAAE,SAAS;aACjB,CAAC,CAAC;YAGH,IAAI,YAAY,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,EAAG,CAAC,CAAC;YAEpE,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YACzD,CAAC;YAGD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CACtC,YAAY,CAAC,EAAE,EACf,WAAW,CAAC,OAAO,EACnB,UAAU,CACX,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE;gBACjD,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,cAAc,EAAE,YAAY,CAAC,EAAE;gBAC/B,SAAS,EAAE,OAAO,CAAC,EAAE;aACtB,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO;gBACP,YAAY;gBACZ,OAAO;aACR,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAC7C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,WAAW;aACZ,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,gBAAgB,CACpB,cAAsB,EACtB,OAAe;QAEf,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;IACjE,CAAC;IAKD,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAE3C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAE7D,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;gBAC5C,OAAO,EAAE,QAAQ,CAAC,IAAI;aACvB,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;gBACzC,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI;aAC/B,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AA7RD,0CA6RC"}