import axios, { AxiosInstance } from 'axios';
import { logger } from '../utils/logger';

// Declare Node.js globals
declare const process: any;

// Interfaces cho Chatwoot API
interface ChatwootConfig {
  baseUrl: string;
  apiAccessToken: string;
  accountId: number;
  inboxId: number;
}

interface ChatwootContact {
  id?: number;
  email?: string;
  name: string;
  phone_number?: string;
  thumbnail?: string;
  additional_attributes?: any;
  contact_inboxes?: ContactInbox[];
  availability_status?: string;
}

interface ContactInbox {
  source_id: string;
  inbox: {
    id: number;
    name: string;
    channel_type: string;
    [key: string]: any;
  };
}

interface ChatwootConversation {
  id: number;
  status?: string;
  inbox_id?: number;
  contact_id?: number;
  [key: string]: any;
}

interface ChatwootMessage {
  id: number;
  content: string;
  message_type: 'incoming' | 'outgoing';
  content_type?: string;
  conversation_id: number;
  inbox_id: number;
  sender: {
    id: number;
    name: string;
    type: 'contact' | 'agent';
  };
  created_at: string;
  [key: string]: any;
}

export class ChatwootService {
  private config: ChatwootConfig;
  private apiClient: AxiosInstance;

  constructor(config: ChatwootConfig) {
    this.config = config;
    this.apiClient = axios.create({
      baseURL: this.config.baseUrl,
      headers: {
        'api_access_token': this.config.apiAccessToken,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
  }

  /**
   * Tìm contact hiện có theo identifier
   */
  async findContactByIdentifier(identifier: string): Promise<ChatwootContact | null> {
    try {
      logger.info('Searching for existing contact', { identifier });

      const response = await this.apiClient.get(`/api/v1/accounts/${this.config.accountId}/contacts/search`, {
        params: {
          q: identifier
        }
      });

      const contacts = response.data.payload || [];

      // Tìm contact có zalo_uid khớp
      const existingContact = contacts.find((contact: any) =>
        contact.additional_attributes?.zalo_uid === identifier
      );

      if (existingContact) {
        logger.info('Found existing contact', {
          contactId: existingContact.id,
          name: existingContact.name
        });
        return existingContact;
      }

      logger.info('No existing contact found');
      return null;

    } catch (error: any) {
      logger.error('Failed to search contact', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        identifier
      });
      return null;
    }
  }

  /**
   * Tạo hoặc tìm contact trong Chatwoot
   */
  async createOrFindContact(contactData: {
    name: string;
    phone_number?: string;
    email?: string;
    identifier: string; // Unique identifier từ Zalo (uidFrom)
  }): Promise<{ contact: ChatwootContact; sourceId: string }> {
    try {
      logger.info('Creating/finding contact in Chatwoot', {
        name: contactData.name,
        identifier: contactData.identifier
      });

      // Tìm contact hiện có trước
      let contact = await this.findContactByIdentifier(contactData.identifier);

      if (!contact) {
        // Tạo contact mới với inbox_id để tự động tạo contact_inbox
        const payload = {
          name: contactData.name,
          phone_number: contactData.phone_number,
          email: contactData.email,
          inbox_id: this.config.inboxId,
          additional_attributes: {
            zalo_uid: contactData.identifier,
            source: 'zalo_webhook'
          }
        };

        const response = await this.apiClient.post(`/api/v1/accounts/${this.config.accountId}/contacts`, payload);
        contact = response.data;

        logger.info('New contact created successfully', {
          contactId: contact.id,
          contactInboxes: contact.contact_inboxes?.length
        });
      } else {
        logger.info('Using existing contact', {
          contactId: contact.id
        });
      }

      // Lấy source_id từ contact_inbox
      const contactInbox = contact.contact_inboxes?.find(
        ci => ci.inbox.id === this.config.inboxId
      );

      if (!contactInbox) {
        // Nếu không có contact_inbox cho inbox này, tạo mới
        logger.info('Creating contact inbox for existing contact');

        const createContactPayload = {
          name: contact.name,
          phone_number: contact.phone_number,
          email: contact.email,
          inbox_id: this.config.inboxId,
          additional_attributes: contact.additional_attributes
        };

        const response = await this.apiClient.post(`/api/v1/accounts/${this.config.accountId}/contacts`, createContactPayload);
        contact = response.data;

        const newContactInbox = contact.contact_inboxes?.find(
          ci => ci.inbox.id === this.config.inboxId
        );

        if (!newContactInbox) {
          throw new Error('Failed to create contact inbox');
        }

        return {
          contact,
          sourceId: newContactInbox.source_id
        };
      }

      return {
        contact,
        sourceId: contactInbox.source_id
      };

    } catch (error: any) {
      logger.error('Failed to create/find contact', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        requestData: {
          name: contactData.name,
          identifier: contactData.identifier,
          inboxId: this.config.inboxId
        }
      });
      throw error;
    }
  }

  /**
   * Tạo conversation mới
   */
  async createConversation(sourceId: string): Promise<ChatwootConversation> {
    try {
      logger.info('Creating conversation in Chatwoot', { sourceId });

      const payload = {
        source_id: sourceId,
        inbox_id: this.config.inboxId
      };

      const response = await this.apiClient.post(`/api/v1/accounts/${this.config.accountId}/conversations`, payload);
      const conversation: ChatwootConversation = response.data;

      logger.info('Conversation created successfully', {
        conversationId: conversation.id
      });

      return conversation;

    } catch (error: any) {
      logger.error('Failed to create conversation', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        requestData: {
          sourceId,
          inboxId: this.config.inboxId
        }
      });
      throw error;
    }
  }

  /**
   * Tạo message trong conversation
   */
  async createMessage(
    conversationId: number,
    content: string,
    messageType: 'incoming' | 'outgoing' = 'incoming'
  ): Promise<ChatwootMessage> {
    try {
      logger.info('Creating message in Chatwoot', {
        conversationId,
        messageType,
        contentLength: content.length
      });

      const payload = {
        content: content,
        message_type: messageType,
        private: false
      };

      const response = await this.apiClient.post(
        `/api/v1/accounts/${this.config.accountId}/conversations/${conversationId}/messages`,
        payload
      );
      const message: ChatwootMessage = response.data;

      logger.info('Message created successfully', {
        messageId: message.id,
        conversationId: message.conversation_id
      });

      return message;

    } catch (error: any) {
      logger.error('Failed to create message', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        requestData: {
          conversationId,
          messageType,
          contentLength: content.length
        }
      });
      throw error;
    }
  }

  /**
   * Tìm conversation hiện có của contact
   */
  async findExistingConversation(contactId: number): Promise<ChatwootConversation | null> {
    try {
      logger.info('Finding existing conversations', { contactId });

      const response = await this.apiClient.get(`/api/v1/accounts/${this.config.accountId}/conversations`, {
        params: {
          inbox_id: this.config.inboxId,
          status: 'open'
        }
      });

      const conversations = response.data.data || [];
      
      // Tìm conversation của contact này
      const existingConversation = conversations.find((conv: any) => 
        conv.meta?.sender?.id === contactId
      );

      if (existingConversation) {
        logger.info('Found existing conversation', {
          conversationId: existingConversation.id
        });
        return existingConversation;
      }

      logger.info('No existing conversation found');
      return null;

    } catch (error: any) {
      logger.error('Failed to find existing conversation', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        requestData: {
          contactId,
          inboxId: this.config.inboxId
        }
      });
      return null;
    }
  }

  /**
   * Xử lý tin nhắn từ Zalo - tạo contact, conversation và message
   */
  async processZaloMessage(messageData: {
    from: string;
    content: string;
    userId: string;
    messageId: string;
    timestamp: string;
  }): Promise<{
    contact: ChatwootContact;
    conversation: ChatwootConversation;
    message: ChatwootMessage;
  }> {
    try {
      logger.info('Processing Zalo message for Chatwoot', {
        from: messageData.from,
        userId: messageData.userId,
        messageId: messageData.messageId
      });

      // 1. Tạo hoặc tìm contact
      const { contact, sourceId } = await this.createOrFindContact({
        name: messageData.from,
        identifier: messageData.userId,
        phone_number: undefined, // Có thể thêm nếu có
        email: undefined // Có thể thêm nếu có
      });

      // 2. Tìm conversation hiện có hoặc tạo mới
      let conversation = await this.findExistingConversation(contact.id!);
      
      if (!conversation) {
        conversation = await this.createConversation(sourceId);
      }

      // 3. Tạo message
      const message = await this.createMessage(
        conversation.id,
        messageData.content,
        'incoming'
      );

      logger.info('Zalo message processed successfully', {
        contactId: contact.id,
        conversationId: conversation.id,
        messageId: message.id
      });

      return {
        contact,
        conversation,
        message
      };

    } catch (error: any) {
      logger.error('Failed to process Zalo message', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        messageData: {
          from: messageData.from,
          userId: messageData.userId,
          messageId: messageData.messageId,
          contentLength: messageData.content.length
        }
      });
      throw error;
    }
  }

  /**
   * Gửi message từ agent (outgoing)
   */
  async sendAgentMessage(
    conversationId: number,
    content: string
  ): Promise<ChatwootMessage> {
    return this.createMessage(conversationId, content, 'outgoing');
  }

  /**
   * Test connection với Chatwoot
   */
  async testConnection(): Promise<boolean> {
    try {
      logger.info('Testing Chatwoot connection');
      
      const response = await this.apiClient.get('/api/v1/profile');
      
      logger.info('Chatwoot connection successful', {
        profile: response.data
      });
      
      return true;
    } catch (error: any) {
      logger.error('Chatwoot connection failed', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        config: {
          baseUrl: this.config.baseUrl,
          hasToken: !!this.config.apiAccessToken,
          accountId: this.config.accountId
        }
      });
      return false;
    }
  }
}
