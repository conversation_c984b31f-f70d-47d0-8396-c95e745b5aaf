"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const errorHandler_1 = require("../middleware/errorHandler");
const errorHandler_2 = require("../middleware/errorHandler");
const logger_1 = require("../utils/logger");
const axios_1 = __importDefault(require("axios"));
const ChatwootService_1 = require("../services/ChatwootService");
const chatwoot_1 = require("../config/chatwoot");
require("../types/api");
const router = (0, express_1.Router)();
let chatwootService = null;
if (chatwoot_1.chatwootConfig.enabled && (0, chatwoot_1.validateChatwootConfig)()) {
    chatwootService = new ChatwootService_1.ChatwootService(chatwoot_1.chatwootConfig);
    logger_1.logger.info('Chatwoot integration enabled', {
        baseUrl: chatwoot_1.chatwootConfig.baseUrl,
        inboxId: chatwoot_1.chatwootConfig.inboxId
    });
}
else {
    logger_1.logger.info('Chatwoot integration disabled or misconfigured');
}
router.post('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const messageData = req.body;
    logger_1.logger.info('Received Zalo message', {
        requestId: req.id,
        messageData: JSON.stringify(messageData, null, 2)
    });
    if (!messageData || !messageData.data) {
        throw new errorHandler_2.AppError('Invalid message format', 400, 'INVALID_MESSAGE_FORMAT');
    }
    const { data, threadId, isSelf } = messageData;
    const { content, dName, uidFrom, msgType, ts } = data;
    logger_1.logger.info('Message details', {
        requestId: req.id,
        from: dName,
        userId: uidFrom,
        content: content,
        messageType: msgType,
        timestamp: ts,
        threadId: threadId,
        isSelf: isSelf
    });
    const autoReplyMessage = "Đã Nhận";
    const webhookUrl = "https://webhook.mooly.vn/webhook/9970c4e7-1891-401a-9182-9e084d435982";
    const webhookPayload = {
        message: autoReplyMessage,
        timestamp: new Date().toISOString(),
        originalMessage: {
            from: dName,
            userId: uidFrom,
            content: content,
            messageType: msgType,
            threadId: threadId,
            receivedAt: ts,
            messageId: data.msgId,
            clientMessageId: data.cliMsgId
        }
    };
    try {
        let chatwootResult = null;
        if (chatwootService) {
            try {
                logger_1.logger.info('Processing message with Chatwoot', {
                    requestId: req.id,
                    from: dName,
                    userId: uidFrom
                });
                chatwootResult = await chatwootService.processZaloMessage({
                    from: dName,
                    content: content,
                    userId: uidFrom,
                    messageId: data.msgId,
                    timestamp: ts
                });
                logger_1.logger.info('Chatwoot processing successful', {
                    requestId: req.id,
                    contactId: chatwootResult.contact.id,
                    conversationId: chatwootResult.conversation.id,
                    messageId: chatwootResult.message.id
                });
            }
            catch (chatwootError) {
                logger_1.logger.error('Chatwoot processing failed', {
                    requestId: req.id,
                    error: chatwootError.message,
                    from: dName,
                    userId: uidFrom
                });
            }
        }
        logger_1.logger.info('Sending auto-reply to webhook', {
            requestId: req.id,
            webhookUrl: webhookUrl,
            payload: webhookPayload
        });
        const webhookResponse = await axios_1.default.post(webhookUrl, webhookPayload, {
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Zalo-Chatbot-API/1.0.0'
            },
            timeout: 10000
        });
        logger_1.logger.info('Webhook response received', {
            requestId: req.id,
            status: webhookResponse.status,
            statusText: webhookResponse.statusText,
            responseData: webhookResponse.data
        });
        res.status(200).json({
            success: true,
            message: 'Message received and processed successfully',
            data: {
                receivedMessage: {
                    from: dName,
                    content: content,
                    messageId: data.msgId,
                    timestamp: ts
                },
                autoReply: {
                    message: autoReplyMessage,
                    sentAt: new Date().toISOString(),
                    webhookStatus: webhookResponse.status
                },
                webhookResponse: {
                    status: webhookResponse.status,
                    data: webhookResponse.data
                },
                chatwoot: chatwootResult ? {
                    enabled: true,
                    contactId: chatwootResult.contact.id,
                    conversationId: chatwootResult.conversation.id,
                    messageId: chatwootResult.message.id,
                    status: 'success'
                } : {
                    enabled: chatwoot_1.chatwootConfig.enabled,
                    status: chatwoot_1.chatwootConfig.enabled ? 'failed' : 'disabled'
                }
            }
        });
    }
    catch (webhookError) {
        logger_1.logger.error('Failed to send webhook', {
            requestId: req.id,
            error: webhookError.message,
            webhookUrl: webhookUrl,
            payload: webhookPayload
        });
        res.status(200).json({
            success: true,
            message: 'Message received but failed to send auto-reply',
            data: {
                receivedMessage: {
                    from: dName,
                    content: content,
                    messageId: data.msgId,
                    timestamp: ts
                },
                autoReply: {
                    message: autoReplyMessage,
                    error: webhookError.message,
                    sentAt: new Date().toISOString()
                }
            }
        });
    }
}));
router.get('/test', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    logger_1.logger.info('Zalo webhook test endpoint accessed', { requestId: req.id });
    res.json({
        success: true,
        message: 'Zalo webhook endpoint is working',
        data: {
            endpoint: '/api/zalo-webhook',
            method: 'POST',
            expectedFormat: {
                type: 0,
                data: {
                    actionId: "string",
                    msgId: "string",
                    cliMsgId: "string",
                    msgType: "string",
                    uidFrom: "string",
                    idTo: "string",
                    dName: "string",
                    ts: "string",
                    status: "number",
                    content: "string",
                },
                threadId: "string",
                isSelf: "boolean"
            },
            autoReplyMessage: "Đã Nhận",
            webhookUrl: "https://webhook.mooly.vn/webhook/9970c4e7-1891-401a-9182-9e084d435982",
            timestamp: new Date().toISOString()
        }
    });
}));
router.post('/test-send', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { message = "Test message from API" } = req.body;
    const webhookUrl = "https://webhook.mooly.vn/webhook/9970c4e7-1891-401a-9182-9e084d435982";
    logger_1.logger.info('Manual webhook test initiated', {
        requestId: req.id,
        message: message
    });
    const testPayload = {
        message: message,
        timestamp: new Date().toISOString(),
        originalMessage: {
            from: "Test User",
            userId: "test-user-id",
            content: "This is a test message",
            messageType: "test",
            threadId: "test-thread-id",
            receivedAt: new Date().toISOString(),
            messageId: "test-msg-id",
            clientMessageId: "test-cli-msg-id"
        }
    };
    try {
        const webhookResponse = await axios_1.default.post(webhookUrl, testPayload, {
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Zalo-Chatbot-API/1.0.0'
            },
            timeout: 10000
        });
        logger_1.logger.info('Test webhook sent successfully', {
            requestId: req.id,
            status: webhookResponse.status,
            responseData: webhookResponse.data
        });
        res.json({
            success: true,
            message: 'Test webhook sent successfully',
            data: {
                sentPayload: testPayload,
                webhookResponse: {
                    status: webhookResponse.status,
                    statusText: webhookResponse.statusText,
                    data: webhookResponse.data
                }
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Test webhook failed', {
            requestId: req.id,
            error: error.message
        });
        throw new errorHandler_2.AppError(`Failed to send test webhook: ${error.message}`, 500, 'WEBHOOK_TEST_FAILED');
    }
}));
router.get('/chatwoot-test', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    logger_1.logger.info('Chatwoot connection test endpoint accessed', { requestId: req.id });
    if (!chatwootService) {
        return res.json({
            success: false,
            message: 'Chatwoot integration is disabled or misconfigured',
            data: {
                enabled: chatwoot_1.chatwootConfig.enabled,
                config: {
                    baseUrl: chatwoot_1.chatwootConfig.baseUrl,
                    inboxId: chatwoot_1.chatwootConfig.inboxId,
                    hasApiToken: !!chatwoot_1.chatwootConfig.apiAccessToken
                }
            }
        });
    }
    try {
        const connectionTest = await chatwootService.testConnection();
        res.json({
            success: connectionTest,
            message: connectionTest ? 'Chatwoot connection successful' : 'Chatwoot connection failed',
            data: {
                enabled: true,
                config: {
                    baseUrl: chatwoot_1.chatwootConfig.baseUrl,
                    inboxId: chatwoot_1.chatwootConfig.inboxId,
                    hasApiToken: !!chatwoot_1.chatwootConfig.apiAccessToken
                },
                connectionStatus: connectionTest ? 'connected' : 'failed'
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Chatwoot connection test failed', {
            requestId: req.id,
            error: error.message
        });
        res.json({
            success: false,
            message: 'Chatwoot connection test failed',
            data: {
                enabled: true,
                error: error.message,
                config: {
                    baseUrl: chatwoot_1.chatwootConfig.baseUrl,
                    inboxId: chatwoot_1.chatwootConfig.inboxId,
                    hasApiToken: !!chatwoot_1.chatwootConfig.apiAccessToken
                }
            }
        });
    }
}));
exports.default = router;
//# sourceMappingURL=zalo-webhook.js.map