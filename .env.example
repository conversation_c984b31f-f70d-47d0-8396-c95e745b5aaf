# Application Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=info
LOG_FORMAT=combined

# Zalo Configuration
ZALO_APP_ID=your_zalo_app_id
ZALO_SECRET_KEY=your_zalo_secret_key
ZALO_OA_ID=your_zalo_oa_id
ZALO_WEBHOOK_URL=https://your-domain.com/webhook

# Chatwoot Integration
# Bật/tắt tích hợp Chatwoot (true/false)
CHATWOOT_ENABLED=true

# URL của Chatwoot instance
# Ví dụ: https://app.chatwoot.com hoặc https://your-chatwoot-domain.com
CHATWOOT_BASE_URL=https://app.chatwoot.com

# API Access Token từ Chatwoot
# Lấy từ: Profile Settings → Access Token
CHATWOOT_API_ACCESS_TOKEN=your_api_access_token_here

# Inbox ID của API channel
# Lấy từ: Settings → Inboxes → API Channel → Settings → Configuration
CHATWOOT_INBOX_ID=123
