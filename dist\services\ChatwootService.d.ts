interface ChatwootConfig {
    baseUrl: string;
    apiAccessToken: string;
    inboxId: number;
}
interface ChatwootContact {
    id?: number;
    email?: string;
    name: string;
    phone_number?: string;
    thumbnail?: string;
    additional_attributes?: any;
    contact_inboxes?: ContactInbox[];
    availability_status?: string;
}
interface ContactInbox {
    source_id: string;
    inbox: {
        id: number;
        name: string;
        channel_type: string;
        [key: string]: any;
    };
}
interface ChatwootConversation {
    id: number;
    status?: string;
    inbox_id?: number;
    contact_id?: number;
    [key: string]: any;
}
interface ChatwootMessage {
    id: number;
    content: string;
    message_type: 'incoming' | 'outgoing';
    content_type?: string;
    conversation_id: number;
    inbox_id: number;
    sender: {
        id: number;
        name: string;
        type: 'contact' | 'agent';
    };
    created_at: string;
    [key: string]: any;
}
export declare class ChatwootService {
    private config;
    private apiClient;
    constructor(config: ChatwootConfig);
    createOrFindContact(contactData: {
        name: string;
        phone_number?: string;
        email?: string;
        identifier: string;
    }): Promise<{
        contact: ChatwootContact;
        sourceId: string;
    }>;
    createConversation(sourceId: string): Promise<ChatwootConversation>;
    createMessage(conversationId: number, content: string, messageType?: 'incoming' | 'outgoing'): Promise<ChatwootMessage>;
    findExistingConversation(contactId: number): Promise<ChatwootConversation | null>;
    processZaloMessage(messageData: {
        from: string;
        content: string;
        userId: string;
        messageId: string;
        timestamp: string;
    }): Promise<{
        contact: ChatwootContact;
        conversation: ChatwootConversation;
        message: ChatwootMessage;
    }>;
    sendAgentMessage(conversationId: number, content: string): Promise<ChatwootMessage>;
    testConnection(): Promise<boolean>;
}
export {};
