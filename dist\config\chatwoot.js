"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.chatwootConfig = void 0;
exports.validateChatwootConfig = validateChatwootConfig;
exports.chatwootConfig = {
    baseUrl: process.env.CHATWOOT_BASE_URL || 'https://app.chatwoot.com',
    apiAccessToken: process.env.CHATWOOT_API_ACCESS_TOKEN || '',
    inboxId: parseInt(process.env.CHATWOOT_INBOX_ID || '0'),
    enabled: process.env.CHATWOOT_ENABLED === 'true'
};
function validateChatwootConfig() {
    if (!exports.chatwootConfig.enabled) {
        return true;
    }
    const errors = [];
    if (!exports.chatwootConfig.baseUrl) {
        errors.push('CHATWOOT_BASE_URL is required');
    }
    if (!exports.chatwootConfig.apiAccessToken) {
        errors.push('CHATWOOT_API_ACCESS_TOKEN is required');
    }
    if (!exports.chatwootConfig.inboxId || exports.chatwootConfig.inboxId === 0) {
        errors.push('CHATWOOT_INBOX_ID is required and must be a valid number');
    }
    if (errors.length > 0) {
        console.error('Chatwoot configuration errors:', errors);
        return false;
    }
    return true;
}
//# sourceMappingURL=chatwoot.js.map