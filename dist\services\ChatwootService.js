"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatwootService = void 0;
const axios_1 = __importDefault(require("axios"));
const logger_1 = require("../utils/logger");
class ChatwootService {
    config;
    apiClient;
    constructor(config) {
        this.config = config;
        this.apiClient = axios_1.default.create({
            baseURL: this.config.baseUrl,
            headers: {
                'api_access_token': this.config.apiAccessToken,
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });
    }
    async createOrFindContact(contactData) {
        try {
            logger_1.logger.info('Creating/finding contact in Chatwoot', {
                name: contactData.name,
                identifier: contactData.identifier
            });
            const payload = {
                name: contactData.name,
                phone_number: contactData.phone_number,
                email: contactData.email,
                inbox_id: this.config.inboxId,
                additional_attributes: {
                    zalo_uid: contactData.identifier,
                    source: 'zalo_webhook'
                }
            };
            const response = await this.apiClient.post('/api/v1/contacts', payload);
            const contact = response.data;
            logger_1.logger.info('Contact created/found successfully', {
                contactId: contact.id,
                contactInboxes: contact.contact_inboxes?.length
            });
            const contactInbox = contact.contact_inboxes?.find(ci => ci.inbox.id === this.config.inboxId);
            if (!contactInbox) {
                throw new Error('Contact inbox not found for the specified inbox ID');
            }
            return {
                contact,
                sourceId: contactInbox.source_id
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to create/find contact', {
                error: error.message,
                response: error.response?.data
            });
            throw error;
        }
    }
    async createConversation(sourceId) {
        try {
            logger_1.logger.info('Creating conversation in Chatwoot', { sourceId });
            const payload = {
                source_id: sourceId,
                inbox_id: this.config.inboxId
            };
            const response = await this.apiClient.post('/api/v1/conversations', payload);
            const conversation = response.data;
            logger_1.logger.info('Conversation created successfully', {
                conversationId: conversation.id
            });
            return conversation;
        }
        catch (error) {
            logger_1.logger.error('Failed to create conversation', {
                error: error.message,
                response: error.response?.data,
                sourceId
            });
            throw error;
        }
    }
    async createMessage(conversationId, content, messageType = 'incoming') {
        try {
            logger_1.logger.info('Creating message in Chatwoot', {
                conversationId,
                messageType,
                contentLength: content.length
            });
            const payload = {
                content: content,
                message_type: messageType,
                private: false
            };
            const response = await this.apiClient.post(`/api/v1/conversations/${conversationId}/messages`, payload);
            const message = response.data;
            logger_1.logger.info('Message created successfully', {
                messageId: message.id,
                conversationId: message.conversation_id
            });
            return message;
        }
        catch (error) {
            logger_1.logger.error('Failed to create message', {
                error: error.message,
                response: error.response?.data,
                conversationId
            });
            throw error;
        }
    }
    async findExistingConversation(contactId) {
        try {
            logger_1.logger.info('Finding existing conversations', { contactId });
            const response = await this.apiClient.get('/api/v1/conversations', {
                params: {
                    inbox_id: this.config.inboxId,
                    status: 'open'
                }
            });
            const conversations = response.data.data || [];
            const existingConversation = conversations.find((conv) => conv.meta?.sender?.id === contactId);
            if (existingConversation) {
                logger_1.logger.info('Found existing conversation', {
                    conversationId: existingConversation.id
                });
                return existingConversation;
            }
            logger_1.logger.info('No existing conversation found');
            return null;
        }
        catch (error) {
            logger_1.logger.error('Failed to find existing conversation', {
                error: error.message,
                contactId
            });
            return null;
        }
    }
    async processZaloMessage(messageData) {
        try {
            logger_1.logger.info('Processing Zalo message for Chatwoot', {
                from: messageData.from,
                userId: messageData.userId,
                messageId: messageData.messageId
            });
            const { contact, sourceId } = await this.createOrFindContact({
                name: messageData.from,
                identifier: messageData.userId,
                phone_number: undefined,
                email: undefined
            });
            let conversation = await this.findExistingConversation(contact.id);
            if (!conversation) {
                conversation = await this.createConversation(sourceId);
            }
            const message = await this.createMessage(conversation.id, messageData.content, 'incoming');
            logger_1.logger.info('Zalo message processed successfully', {
                contactId: contact.id,
                conversationId: conversation.id,
                messageId: message.id
            });
            return {
                contact,
                conversation,
                message
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to process Zalo message', {
                error: error.message,
                messageData
            });
            throw error;
        }
    }
    async sendAgentMessage(conversationId, content) {
        return this.createMessage(conversationId, content, 'outgoing');
    }
    async testConnection() {
        try {
            logger_1.logger.info('Testing Chatwoot connection');
            const response = await this.apiClient.get('/api/v1/profile');
            logger_1.logger.info('Chatwoot connection successful', {
                profile: response.data
            });
            return true;
        }
        catch (error) {
            logger_1.logger.error('Chatwoot connection failed', {
                error: error.message,
                response: error.response?.data
            });
            return false;
        }
    }
}
exports.ChatwootService = ChatwootService;
//# sourceMappingURL=ChatwootService.js.map